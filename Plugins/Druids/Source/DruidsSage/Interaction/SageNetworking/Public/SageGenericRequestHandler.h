#pragma once

#include <CoreMinimal.h>
#include <Interfaces/IHttpRequest.h>
#include <Dom/JsonObject.h>
#include <Dom/JsonValue.h>

#include "DruidsSageCommonTypes.h"

/**
 * Utility class for handling generic request processing and response sending
 * Used by both UDruidsSageChatRequest_v2 and UDruidsSageInvocationRequest
 */
class SAGENETWORKING_API FSageGenericRequestHandler
{
public:
	FSageGenericRequestHandler();
	~FSageGenericRequestHandler();

	/**
	 * Process a generic request with the given parameters
	 * @param RequestType The type of request to process
	 * @param Payload The JSON payload for the request
	 * @param ThreadId The thread ID for the request
	 * @param RequestId The unique request ID
	 * @param ResponseUrl The URL to send the response to
	 * @param CommonOptions The common options containing API key and other settings
	 */
	void ProcessGenericRequest(const FString& RequestType, 
	                          const TSharedPtr<FJsonObject>& Payload, 
	                          const FString& ThreadId,
	                          const FString& RequestId, 
	                          const FString& ResponseUrl,
	                          const FDruidsSageCommonOptions& CommonOptions);

	/**
	 * Send a generic response to the specified URL
	 * @param GenericResponse The JSON response object to send
	 * @param ResponseUrl The URL to send the response to
	 * @param CommonOptions The common options containing API key and other settings
	 */
	void SendGenericResponseToUrl(const TSharedPtr<FJsonObject>& GenericResponse,
	                             const FString& ResponseUrl,
	                             const FDruidsSageCommonOptions& CommonOptions);

	/**
	 * Check if a request has already been processed
	 * @param RequestId The request ID to check
	 * @return True if the request has been processed, false otherwise
	 */
	bool IsRequestProcessed(const FString& RequestId) const;

	/**
	 * Clear all processed request tracking
	 */
	void ClearProcessedRequests();

protected:
	TSharedPtr<FJsonObject> ProcessPythonPathsRequest(const TArray<TSharedPtr<FJsonValue>> PythonPathsArray);

	TSharedPtr<FJsonObject> ProcessInvocation(const FString InvocationString);

	/**
	 * Process a list_docs request to get available documents
	 */
	TSharedPtr<FJsonObject> ProcessListDocsRequest();

	/**
	 * Process a get_doc_contents request to retrieve document contents
	 * @param DocumentPath The path to the document to retrieve
	 */
	TSharedPtr<FJsonObject> ProcessGetDocContentsRequest(const FString& DocumentPath);

	/**
	 * Process an add_doc request to add a new document
	 * @param DocumentPath The path for the new document
	 * @param Contents The contents of the document
	 */
	TSharedPtr<FJsonObject> ProcessAddDocRequest(const FString& DocumentPath, const FString& Contents);

	/**
	 * Process a modify_doc request to modify an existing document
	 * @param DocumentPath The path to the document to modify
	 * @param Contents The new contents of the document
	 */
	TSharedPtr<FJsonObject> ProcessModifyDocRequest(const FString& DocumentPath, const FString& Contents);

	/**
	 * Process a delete_doc request to delete a document
	 * @param DocumentPath The path to the document to delete
	 */
	TSharedPtr<FJsonObject> ProcessDeleteDocRequest(const FString& DocumentPath);

private:
	// Set to track which generic requests have been processed
	TSet<FString> GenericRequestResponsesDone;

	// Set to track in-flight HTTP responses
	TSet<TSharedPtr<IHttpRequest, ESPMode::ThreadSafe>> InFlightResponses;

	// Mutex to protect InFlightResponses collection
	mutable FCriticalSection InFlightResponseMutex;
};
