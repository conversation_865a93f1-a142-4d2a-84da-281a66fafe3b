#include "SageGenericRequestHandler.h"

#include <HttpModule.h>
#include <Interfaces/IHttpRequest.h>
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>
#include <Async/Async.h>

#include "LogDruids.h"
#include "SimpleJSON.h"
#include "SagePythonWorker.h"
#include "SageDocumentsUtility.h"

FSageGenericRequestHandler::FSageGenericRequestHandler()
{
}

FSageGenericRequestHandler::~FSageGenericRequestHandler()
{
	// Clean up any remaining in-flight requests
	FScopeLock Lock(&InFlightResponseMutex);
	InFlightResponses.Empty();
}

TSharedPtr<FJsonObject> FSageGenericRequestHandler::ProcessPythonPathsRequest(const TArray<TSharedPtr<FJsonValue>> PythonPathsArray)
{
	FJsonObject ResponseOutput;

	TArray<FString> PythonPathStrings;
	for (const TSharedPtr PythonPath : PythonPathsArray)
	{
		PythonPathStrings.Add(PythonPath->AsString());
	}

	TMap<const FString, const FString> OutputResults;
	USagePythonWorker* Worker = USagePythonWorker::GetInstance();

	if (bool bSuccess = Worker->CheckPythonPaths(PythonPathStrings, OutputResults))
	{
		for (TPair OutputResult: OutputResults)
		{
			ResponseOutput.SetStringField(OutputResult.Key, OutputResult.Value);
		}
	}

	return MakeShared<FJsonObject>(ResponseOutput);
}

TSharedPtr<FJsonObject> FSageGenericRequestHandler::ProcessInvocation(const FString InvocationString)
{
	SimpleJSON OutputJSON;
	FString OutResultMessage;

	USagePythonWorker* Worker = USagePythonWorker::GetInstance();
	bool bSuccess = Worker->ExecutePythonCode(InvocationString, OutResultMessage);

	OutputJSON["success"] = bSuccess;
	OutputJSON["output_message"] = OutResultMessage;

	return OutputJSON.GetJsonObject();
}

void FSageGenericRequestHandler::ProcessGenericRequest(const FString& RequestType,
                                                       const TSharedPtr<FJsonObject>& Payload,
                                                       const FString& ThreadId,
                                                       const FString& RequestId,
                                                       const FString& ResponseUrl,
                                                       const FDruidsSageCommonOptions& CommonOptions)
{
	// Check if we've already processed this request
	if (GenericRequestResponsesDone.Contains(RequestId))
	{
		return;
	}

	// Mark this request as processed
	GenericRequestResponsesDone.Add(RequestId);

	SimpleJSON PayloadJSON(*Payload.Get());

	SimpleJSON ResponsePayload;
	if (RequestType == TEXT("check_python_paths"))
	{
		if (const TArray<TSharedPtr<FJsonValue>> PythonPathsArray = PayloadJSON["python_paths"].AsNativeArray(); !PythonPathsArray.IsEmpty())
		{
			ResponsePayload = SimpleJSON(*ProcessPythonPathsRequest(PythonPathsArray).Get());
		}
		else
		{
			UE_LOG(LogDruidsSage, Display, TEXT("No python paths found in payload:%s"), *PayloadJSON.ToString());
			return;
		}
	}
	else if (RequestType == TEXT("invocation_request"))
	{
		if (const FString InvocationString = PayloadJSON["invocation"].AsString(); !InvocationString.IsEmpty())
		{
			ResponsePayload = SimpleJSON(*ProcessInvocation(InvocationString).Get());
		}
	}
	else if (RequestType == TEXT("list_docs"))
	{
		ResponsePayload = SimpleJSON(*ProcessListDocsRequest().Get());
	}
	else if (RequestType == TEXT("get_doc_contents"))
	{
		if (const FString DocumentPath = PayloadJSON["filename"].AsString(); !DocumentPath.IsEmpty())
		{
			ResponsePayload = SimpleJSON(*ProcessGetDocContentsRequest(DocumentPath).Get());
		}
		else
		{
			UE_LOG(LogDruidsSage, Display, TEXT("No filename found in payload:%s"), *PayloadJSON.ToString());
			return;
		}
	}
	else if (RequestType == TEXT("add_doc"))
	{
		const FString DocumentPath = PayloadJSON["filename"].AsString();
		const FString Contents = PayloadJSON["contents"].AsString();
		if (!DocumentPath.IsEmpty())
		{
			ResponsePayload = SimpleJSON(*ProcessAddDocRequest(DocumentPath, Contents).Get());
		}
		else
		{
			UE_LOG(LogDruidsSage, Display, TEXT("No filename found in payload:%s"), *PayloadJSON.ToString());
			return;
		}
	}
	else if (RequestType == TEXT("modify_doc"))
	{
		const FString DocumentPath = PayloadJSON["filename"].AsString();
		const FString Contents = PayloadJSON["contents"].AsString();
		if (!DocumentPath.IsEmpty())
		{
			ResponsePayload = SimpleJSON(*ProcessModifyDocRequest(DocumentPath, Contents).Get());
		}
		else
		{
			UE_LOG(LogDruidsSage, Display, TEXT("No filename found in payload:%s"), *PayloadJSON.ToString());
			return;
		}
	}
	else if (RequestType == TEXT("delete_doc"))
	{
		const FString DocumentPath = PayloadJSON["filename"].AsString();
		if (!DocumentPath.IsEmpty())
		{
			ResponsePayload = SimpleJSON(*ProcessDeleteDocRequest(DocumentPath).Get());
		}
		else
		{
			UE_LOG(LogDruidsSage, Display, TEXT("No filename found in payload:%s"), *PayloadJSON.ToString());
			return;
		}
	}
	else
	{
		UE_LOG(LogDruidsSage, Display, TEXT("Unknown request type:%s, payload:%s"), *RequestType, *PayloadJSON.ToString());
		return;
	}

	UE_LOG(LogDruidsSage, Display, TEXT("ProcessGenericRequest: RequestType=%s, RequestId=%s, ResponsePayload=%s"),
	       *RequestType, *RequestId, *ResponsePayload.ToString());

	// Create response object and send to URL
	if (!ResponseUrl.IsEmpty())
	{
		// Create the response object
		TSharedPtr<FJsonObject> ResponseObj = MakeShared<FJsonObject>();
		ResponseObj->SetStringField(TEXT("thread_id"), ThreadId);
		ResponseObj->SetStringField(TEXT("request_id"), RequestId);
		ResponseObj->SetStringField(TEXT("type"), RequestType);
		ResponseObj->SetObjectField(TEXT("results"), ResponsePayload.GetJsonObject());

		// Send the response using the generic response sender
		SendGenericResponseToUrl(ResponseObj, ResponseUrl, CommonOptions);
	}
}

void FSageGenericRequestHandler::SendGenericResponseToUrl(const TSharedPtr<FJsonObject>& GenericResponse,
                                                         const FString& ResponseUrl,
                                                         const FDruidsSageCommonOptions& CommonOptions)
{
	//
	// Set up the request
	//
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> GenericHttpRequest = FHttpModule::Get().CreateRequest();
	{
		GenericHttpRequest->SetURL(ResponseUrl);
		GenericHttpRequest->SetVerb("POST");
		GenericHttpRequest->SetHeader("Content-Type", "application/json");

		// Use the DruidsUserToken as the value for the Authorization header
		FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *CommonOptions.APIKey.ToString());
		GenericHttpRequest->SetHeader("Authorization", AuthHeader);
	}

	//
	// Store the request while it is in flight
	//
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> StoredRequest;
	{
		FScopeLock Lock(&InFlightResponseMutex);

		// Store the request to keep it alive until completion
		StoredRequest = GenericHttpRequest;
		InFlightResponses.Add(StoredRequest);
	}

	//
	// Add the generic response
	//
	{
		// Serialize the JSON object to a string
		FString RequestBodyString;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBodyString);
		FJsonSerializer::Serialize(GenericResponse.ToSharedRef(), Writer);

		// Set the request content
		GenericHttpRequest->SetContentAsString(RequestBodyString);
	}

	//
	// Fire the request off
	//
	{
		// Add completion handler to clean up the reference when done
		GenericHttpRequest->OnProcessRequestComplete().BindLambda(
			[this, StoredRequest]
			(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bSuccess)
		{
			// Remove when complete
			FScopeLock Lock(&InFlightResponseMutex);
			InFlightResponses.Remove(StoredRequest);
		});

		AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [GenericHttpRequest]
		{
			GenericHttpRequest->ProcessRequest();
		});
	}
}

TSharedPtr<FJsonObject> FSageGenericRequestHandler::ProcessListDocsRequest()
{
	SimpleJSON ResponseJSON;

	// Get the list of documents from the utility
	TArray<FString> DocumentList = USageDocumentsUtility::GetDocumentList();

	// Convert to JSON array
	TArray<TSharedPtr<FJsonValue>> DocumentArray;
	for (const FString& DocumentName : DocumentList)
	{
		DocumentArray.Add(MakeShareable(new FJsonValueString(DocumentName)));
	}

	ResponseJSON["documents"] = DocumentArray;
	ResponseJSON["success"] = true;
	ResponseJSON["count"] = DocumentList.Num();

	return ResponseJSON.GetJsonObject();
}

TSharedPtr<FJsonObject> FSageGenericRequestHandler::ProcessGetDocContentsRequest(const FString& DocumentPath)
{
	SimpleJSON ResponseJSON;

	// Get the document contents from the utility
	FString DocumentContents = USageDocumentsUtility::GetDocumentContents(DocumentPath);

	if (!DocumentContents.IsEmpty())
	{
		ResponseJSON["success"] = true;
		ResponseJSON["filename"] = DocumentPath;
		ResponseJSON["contents"] = DocumentContents;
	}
	else
	{
		ResponseJSON["success"] = false;
		ResponseJSON["filename"] = DocumentPath;
		ResponseJSON["error"] = FString::Printf(TEXT("Document '%s' not found or could not be read"), *DocumentPath);
	}

	return ResponseJSON.GetJsonObject();
}

bool FSageGenericRequestHandler::IsRequestProcessed(const FString& RequestId) const
{
	return GenericRequestResponsesDone.Contains(RequestId);
}

void FSageGenericRequestHandler::ClearProcessedRequests()
{
	GenericRequestResponsesDone.Empty();
}

TSharedPtr<FJsonObject> FSageGenericRequestHandler::ProcessAddDocRequest(const FString& DocumentPath, const FString& Contents)
{
	SimpleJSON ResponseJSON;

	// Attempt to add the document using the utility
	bool bSuccess = USageDocumentsUtility::AddDocument(DocumentPath, Contents);

	if (bSuccess)
	{
		ResponseJSON["success"] = true;
		ResponseJSON["filename"] = DocumentPath;
		ResponseJSON["message"] = FString::Printf(TEXT("Document '%s' added successfully"), *DocumentPath);
	}
	else
	{
		ResponseJSON["success"] = false;
		ResponseJSON["filename"] = DocumentPath;
		ResponseJSON["error"] = FString::Printf(TEXT("Failed to add document '%s'"), *DocumentPath);
	}

	return ResponseJSON.GetJsonObject();
}

TSharedPtr<FJsonObject> FSageGenericRequestHandler::ProcessModifyDocRequest(const FString& DocumentPath, const FString& Contents)
{
	SimpleJSON ResponseJSON;

	// Attempt to modify the document using the utility
	bool bSuccess = USageDocumentsUtility::ModifyDocument(DocumentPath, Contents);

	if (bSuccess)
	{
		ResponseJSON["success"] = true;
		ResponseJSON["filename"] = DocumentPath;
		ResponseJSON["message"] = FString::Printf(TEXT("Document '%s' modified successfully"), *DocumentPath);
	}
	else
	{
		ResponseJSON["success"] = false;
		ResponseJSON["filename"] = DocumentPath;
		ResponseJSON["error"] = FString::Printf(TEXT("Failed to modify document '%s'"), *DocumentPath);
	}

	return ResponseJSON.GetJsonObject();
}

TSharedPtr<FJsonObject> FSageGenericRequestHandler::ProcessDeleteDocRequest(const FString& DocumentPath)
{
	SimpleJSON ResponseJSON;

	// Attempt to delete the document using the utility
	bool bSuccess = USageDocumentsUtility::DeleteDocument(DocumentPath);

	if (bSuccess)
	{
		ResponseJSON["success"] = true;
		ResponseJSON["filename"] = DocumentPath;
		ResponseJSON["message"] = FString::Printf(TEXT("Document '%s' deleted successfully"), *DocumentPath);
	}
	else
	{
		ResponseJSON["success"] = false;
		ResponseJSON["filename"] = DocumentPath;
		ResponseJSON["error"] = FString::Printf(TEXT("Failed to delete document '%s'"), *DocumentPath);
	}

	return ResponseJSON.GetJsonObject();
}