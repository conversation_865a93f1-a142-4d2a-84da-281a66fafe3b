using UnrealBuildTool;

public class SageDocuments : ModuleRules
{
    public SageDocuments(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                
                "Json"
            }
        );

        // Add editor-specific dependencies only when building for editor
        if (Target.bBuildEditor == true)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                }
            );
        }

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",
                
                "SageJSONUtilities",
            });
    }
}
