#include "SageDocumentsUtility.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"

// Constants
namespace SageDocumentsConstants
{
    // Directory names
    static const TCHAR* DruidsSageDir = TEXT("DruidsSage");
    static const TCHAR* DocsDir = TEXT("Docs");
    static const TCHAR* SourceSubDir = TEXT("Source");
    static const TCHAR* WorkingSubDir = TEXT("Working");
    static const TCHAR* MasterDocsDir = TEXT("DruidsSageDocsSource");

    // File extensions
    static const TArray<FString> SupportedExtensions = {
        TEXT("md"),
        TEXT("txt"),
        TEXT("json"),
        TEXT("xml"),
        TEXT("html"),
        TEXT("csv")
    };

    // Special files
    static const TCHAR* ReadmeFileName = TEXT("README_SageDocuments.md");

    // Path separators
    static const TCHAR* PathSeparator = TEXT("/");
}

TArray<FString> USageDocumentsUtility::GetDocumentList()
{
    TArray<FString> DocumentList;
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    // Get base docs path (without Source or Working subdirectory)
    FString BaseDocsPath = FPaths::Combine(FPaths::ProjectSavedDir(), SageDocumentsConstants::DruidsSageDir, SageDocumentsConstants::DocsDir);

    // Check Source directory
    FString SourceDocsPath = FPaths::Combine(BaseDocsPath, SageDocumentsConstants::SourceSubDir);
    if (PlatformFile.DirectoryExists(*SourceDocsPath))
    {
        TArray<FString> FoundFiles;
        PlatformFile.FindFilesRecursively(FoundFiles, *SourceDocsPath, nullptr);

        for (const FString& FilePath : FoundFiles)
        {
            FString Extension = FPaths::GetExtension(FilePath).ToLower();
            if (SageDocumentsConstants::SupportedExtensions.Contains(Extension))
            {
                // Get relative path from base docs directory
                FString RelativePath = FilePath;
                MakeRelativePathUnder(BaseDocsPath, RelativePath);
                DocumentList.Add(*RelativePath.Replace(TEXT("\\"), TEXT("/")));
            }
        }
    }

    // Check Working directory
    FString WorkingDocsPath = FPaths::Combine(BaseDocsPath, SageDocumentsConstants::WorkingSubDir);
    if (PlatformFile.DirectoryExists(*WorkingDocsPath))
    {
        TArray<FString> FoundFiles;
        PlatformFile.FindFilesRecursively(FoundFiles, *WorkingDocsPath, nullptr);

        for (const FString& FilePath : FoundFiles)
        {
            FString Extension = FPaths::GetExtension(FilePath).ToLower();
            if (SageDocumentsConstants::SupportedExtensions.Contains(Extension))
            {
                // Get relative path from base docs directory
                FString RelativePath = FilePath;
                MakeRelativePathUnder(BaseDocsPath, RelativePath);
                DocumentList.Add(*RelativePath.Replace(TEXT("\\"), TEXT("/")));
            }
        }
    }

    DocumentList.Sort();
    return DocumentList;
}

FString USageDocumentsUtility::GetDocumentContents(const FString& DocumentPath)
{
    // Get base docs path
    FString BaseDocsPath = FPaths::Combine(FPaths::ProjectSavedDir(), SageDocumentsConstants::DruidsSageDir, SageDocumentsConstants::DocsDir);
    FString FullPath = FPaths::Combine(BaseDocsPath, DocumentPath);

    FString Contents;
    if (FFileHelper::LoadFileToString(Contents, *FullPath))
    {
        return Contents;
    }

    return FString();
}

void USageDocumentsUtility::MakeRelativePathUnder(const FString& SourcePath, FString& RelativePath)
{
    FPaths::MakePathRelativeTo(RelativePath, *SourcePath);

    // Extract the path under the containing folder
    int32 Index;
    if (RelativePath.FindChar(*SageDocumentsConstants::PathSeparator, Index))
    {
        RelativePath = RelativePath.Mid(Index + 1);
    }
}

void USageDocumentsUtility::SyncDocuments()
{
    FString SourcePath = GetMasterDocsPath();
    FString DestPath = GetSavedDocsPath();

    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    // Ensure destination directory exists
    EnsureSavedDocsDirectoryExists();

    // If source directory doesn't exist, clear destination and return
    if (!PlatformFile.DirectoryExists(*SourcePath))
    {
        if (PlatformFile.DirectoryExists(*DestPath))
        {
            PlatformFile.DeleteDirectoryRecursively(*DestPath);
            EnsureSavedDocsDirectoryExists(); // Recreate empty directory
        }
        return;
    }

    // Find all files in source directory
    TArray<FString> SourceFiles;
    PlatformFile.FindFilesRecursively(SourceFiles, *SourcePath, nullptr);

    // Build set of expected destination files (excluding README)
    TSet<FString> ExpectedDestFiles;

    // Copy each file to destination
    for (const FString& SourceFile : SourceFiles)
    {
        // Skip README file - it should only exist in DruidsSageDocsSource
        FString FileName = FPaths::GetCleanFilename(SourceFile);
        if (FileName.Equals(SageDocumentsConstants::ReadmeFileName, ESearchCase::IgnoreCase))
        {
            continue;
        }

        // Get relative path from source root (this strips DruidsSageDocsSource/)
        FString RelativePath = SourceFile;
        MakeRelativePathUnder(SourcePath, RelativePath);

        // Build destination path preserving subdirectory structure
        FString DestFile = FPaths::Combine(DestPath, RelativePath);

        // Add to expected files set
        ExpectedDestFiles.Add(DestFile);

        // Check if we need to copy (file doesn't exist or is newer)
        bool bShouldCopy = false;
        if (!PlatformFile.FileExists(*DestFile))
        {
            bShouldCopy = true;
        }
        else
        {
            // Compare modification times
            FDateTime SourceTime = PlatformFile.GetTimeStamp(*SourceFile);
            FDateTime DestTime = PlatformFile.GetTimeStamp(*DestFile);
            bShouldCopy = SourceTime > DestTime;
        }

        if (bShouldCopy)
        {
            CopyFileWithDirectories(SourceFile, DestFile);
        }
    }

    // Remove files from destination that no longer exist in source
    TArray<FString> ExistingDestFiles;
    PlatformFile.FindFilesRecursively(ExistingDestFiles, *DestPath, nullptr);

    for (const FString& ExistingFile : ExistingDestFiles)
    {
        if (!ExpectedDestFiles.Contains(ExistingFile))
        {
            PlatformFile.DeleteFile(*ExistingFile);
            UE_LOG(LogTemp, Log, TEXT("Removed obsolete document: %s"), *ExistingFile);
        }
    }
}

FString USageDocumentsUtility::GetSavedDocsPath()
{
    return FPaths::Combine(FPaths::ProjectSavedDir(), SageDocumentsConstants::DruidsSageDir, SageDocumentsConstants::DocsDir, SageDocumentsConstants::SourceSubDir);
}

FString USageDocumentsUtility::GetMasterDocsPath()
{
    return FPaths::Combine(FPaths::ProjectDir(), SageDocumentsConstants::MasterDocsDir);
}

FString USageDocumentsUtility::GetWorkingDocsPath()
{
    return FPaths::Combine(FPaths::ProjectSavedDir(), SageDocumentsConstants::DruidsSageDir, SageDocumentsConstants::DocsDir, SageDocumentsConstants::WorkingSubDir);
}

void USageDocumentsUtility::EnsureSavedDocsDirectoryExists()
{
    FString SavedDocsPath = GetSavedDocsPath();
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    if (!PlatformFile.DirectoryExists(*SavedDocsPath))
    {
        PlatformFile.CreateDirectoryTree(*SavedDocsPath);
    }
}

void USageDocumentsUtility::EnsureWorkingDocsDirectoryExists()
{
    FString WorkingDocsPath = GetWorkingDocsPath();
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    if (!PlatformFile.DirectoryExists(*WorkingDocsPath))
    {
        PlatformFile.CreateDirectoryTree(*WorkingDocsPath);
    }
}

bool USageDocumentsUtility::CopyFileWithDirectories(const FString& SourcePath, const FString& DestPath)
{
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    // Ensure destination directory exists
    FString DestDir = FPaths::GetPath(DestPath);
    if (!PlatformFile.DirectoryExists(*DestDir))
    {
        PlatformFile.CreateDirectoryTree(*DestDir);
    }

    return PlatformFile.CopyFile(*DestPath, *SourcePath);
}

bool USageDocumentsUtility::AddDocument(const FString& DocumentPath, const FString& Contents)
{
    // Validate that the path is for Working directory
    FString CleanPath = DocumentPath;
    if (CleanPath.StartsWith(TEXT("/Docs/")))
    {
        CleanPath = CleanPath.Mid(6); // Remove "/Docs/" prefix
    }
    else if (CleanPath.StartsWith(TEXT("Docs/")))
    {
        CleanPath = CleanPath.Mid(5); // Remove "Docs/" prefix
    }

    // Ensure the path is in the Working directory
    if (!CleanPath.StartsWith(TEXT("Working/")))
    {
        UE_LOG(LogTemp, Warning, TEXT("AddDocument: Path must be in Working directory. Got: %s"), *DocumentPath);
        return false;
    }

    // Validate file extension
    FString Extension = FPaths::GetExtension(CleanPath).ToLower();
    if (!SageDocumentsConstants::SupportedExtensions.Contains(Extension))
    {
        UE_LOG(LogTemp, Warning, TEXT("AddDocument: Unsupported file extension: %s"), *Extension);
        return false;
    }

    // Ensure Working directory exists
    EnsureWorkingDocsDirectoryExists();

    // Get full path
    FString BaseDocsPath = FPaths::Combine(FPaths::ProjectSavedDir(), SageDocumentsConstants::DruidsSageDir, SageDocumentsConstants::DocsDir);
    FString FullPath = FPaths::Combine(BaseDocsPath, CleanPath);

    // Ensure subdirectories exist
    FString DestDir = FPaths::GetPath(FullPath);
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*DestDir))
    {
        PlatformFile.CreateDirectoryTree(*DestDir);
    }

    // Save the file
    return FFileHelper::SaveStringToFile(Contents, *FullPath);
}
bool USageDocumentsUtility::ModifyDocument(const FString& DocumentPath, const FString& Contents)
{
    // Validate that the path is for Working directory
    FString CleanPath = DocumentPath;
    if (CleanPath.StartsWith(TEXT("/Docs/")))
    {
        CleanPath = CleanPath.Mid(6); // Remove "/Docs/" prefix
    }
    else if (CleanPath.StartsWith(TEXT("Docs/")))
    {
        CleanPath = CleanPath.Mid(5); // Remove "Docs/" prefix
    }

    // Ensure the path is in the Working directory
    if (!CleanPath.StartsWith(TEXT("Working/")))
    {
        UE_LOG(LogTemp, Warning, TEXT("ModifyDocument: Path must be in Working directory. Got: %s"), *DocumentPath);
        return false;
    }

    // Get full path
    FString BaseDocsPath = FPaths::Combine(FPaths::ProjectSavedDir(), SageDocumentsConstants::DruidsSageDir, SageDocumentsConstants::DocsDir);
    FString FullPath = FPaths::Combine(BaseDocsPath, CleanPath);

    // Check if file exists
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.FileExists(*FullPath))
    {
        UE_LOG(LogTemp, Warning, TEXT("ModifyDocument: Document does not exist: %s"), *FullPath);
        return false;
    }

    // Save the file with new contents
    return FFileHelper::SaveStringToFile(Contents, *FullPath);
}

bool USageDocumentsUtility::DeleteDocument(const FString& DocumentPath)
{
    // Validate that the path is for Working directory
    FString CleanPath = DocumentPath;
    if (CleanPath.StartsWith(TEXT("/Docs/")))
    {
        CleanPath = CleanPath.Mid(6); // Remove "/Docs/" prefix
    }
    else if (CleanPath.StartsWith(TEXT("Docs/")))
    {
        CleanPath = CleanPath.Mid(5); // Remove "Docs/" prefix
    }

    // Ensure the path is in the Working directory
    if (!CleanPath.StartsWith(TEXT("Working/")))
    {
        UE_LOG(LogTemp, Warning, TEXT("DeleteDocument: Path must be in Working directory. Got: %s"), *DocumentPath);
        return false;
    }

    // Get full path
    FString BaseDocsPath = FPaths::Combine(FPaths::ProjectSavedDir(), SageDocumentsConstants::DruidsSageDir, SageDocumentsConstants::DocsDir);
    FString FullPath = FPaths::Combine(BaseDocsPath, CleanPath);

    // Check if file exists
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.FileExists(*FullPath))
    {
        UE_LOG(LogTemp, Warning, TEXT("DeleteDocument: Document does not exist: %s"), *FullPath);
        return false;
    }

    // Delete the file
    return PlatformFile.DeleteFile(*FullPath);
}

