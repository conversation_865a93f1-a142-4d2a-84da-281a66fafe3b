#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "SageDocumentsUtility.generated.h"

/**
 * Utility class for managing Sage documents
 */
UCLASS()
class SAGEDOCUMENTS_API USageDocumentsUtility : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    /**
     * Gets a list of all documents in both Source and Working directories
     * @return Array of document paths starting with /Docs/
     */
    UFUNCTION(BlueprintCallable, Category = "Sage Documents")
    static TArray<FString> GetDocumentList();
    
    /**
     * Gets the contents of a specific document
     * @param DocumentPath The path to the document file (can start with /Docs/ or be relative)
     * @return The contents of the document, or empty string if not found
     */
    UFUNCTION(BlueprintCallable, Category = "Sage Documents")
    static FString GetDocumentContents(const FString& DocumentPath);
    static void MakeRelativePathUnder(const FString& SourcePath, FString& RelativePath);

    /**
     * Syncs documents from DruidsSageDocsSource to Saved/DruidsSage/Docs/Source/
     */
    UFUNCTION(BlueprintCallable, Category = "Sage Documents")
    static void SyncDocuments();

    /**
     * Adds a new document to the Working directory
     * @param DocumentPath The path for the document (should start with /Docs/Working/)
     * @param Contents The contents of the document
     * @return True if the document was successfully added, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Sage Documents")
    static bool AddDocument(const FString& DocumentPath, const FString& Contents);

    /**
     * Modifies an existing document in the Working directory
     * @param DocumentPath The path to the document (should start with /Docs/Working/)
     * @param Contents The new contents of the document
     * @return True if the document was successfully modified, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Sage Documents")
    static bool ModifyDocument(const FString& DocumentPath, const FString& Contents);

    /**
     * Deletes a document from the Working directory
     * @param DocumentPath The path to the document (should start with /Docs/Working/)
     * @return True if the document was successfully deleted, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Sage Documents")
    static bool DeleteDocument(const FString& DocumentPath);
    
    /**
     * Gets the full path to the saved docs directory
     */
    static FString GetSavedDocsPath();
    
    /**
     * Gets the full path to the source docs directory
     */
    static FString GetMasterDocsPath();

    /**
     * Gets the full path to the working docs directory
     */
    static FString GetWorkingDocsPath();
    
private:
    /**
     * Ensures the saved docs directory exists
     */
    static void EnsureSavedDocsDirectoryExists();

    /**
     * Ensures the working docs directory exists
     */
    static void EnsureWorkingDocsDirectoryExists();
    
    /**
     * Copies a file from source to destination, creating directories as needed
     */
    static bool CopyFileWithDirectories(const FString& SourcePath, const FString& DestPath);
};
